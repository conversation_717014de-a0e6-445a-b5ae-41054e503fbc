import type { RightPanelProps } from "@/types";

import { But<PERSON> } from "@heroui/button";
import React from "react";

import LabelAssignmentPanel from "./LabelAssignmentPanel.tsx";

import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";
import { closeRightPanel } from "@/store/uiSlice.ts";

// Icons
const CloseIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      d="M6 18L18 6M6 6l12 12"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
    />
  </svg>
);

const RightPanel: React.FC<RightPanelProps> = ({ className }) => {
  const dispatch = useAppDispatch();
  const { activeChat } = useAppSelector((state) => state.message);

  const handleClose = () => {
    dispatch(closeRightPanel());
  };

  if (!activeChat) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <p className="text-gray-500 dark:text-gray-400">No chat selected</p>
      </div>
    );
  }

  return (
    <div
      className={`flex flex-col h-full bg-white dark:bg-gray-800 shadow-md dark:shadow-black/25 border-l border-gray-200 dark:border-gray-600 ${className}`}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="font-semibold text-gray-900 dark:text-white">
            Contact Info
          </h2>
          <Button
            isIconOnly
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            variant="light"
            onPress={handleClose}
          >
            <CloseIcon />
          </Button>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Profile Section */}
        <div className="p-6 text-center border-b border-gray-200 dark:border-gray-700">
          <div className="w-24 h-24 mx-auto mb-4 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-600">
            <div className="w-full h-full flex items-center justify-center bg-blue-500 text-white font-bold text-2xl">
              {activeChat.name.charAt(0).toUpperCase()}
            </div>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {activeChat.name}
          </h3>

          <p className="text-gray-600 dark:text-gray-400 mb-3">
            {activeChat.phoneNumber}
          </p>

          {/* Label */}
          {activeChat.label && (
            <div className="flex flex-wrap justify-center gap-2 mt-3">
              <span className="inline-block bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs px-2 py-1 rounded-full">
                {activeChat.label.name}
              </span>
            </div>
          )}
        </div>

        {/* Label Assignment Section */}
        <LabelAssignmentPanel chat={activeChat} />
      </div>
    </div>
  );
};

export default RightPanel;
