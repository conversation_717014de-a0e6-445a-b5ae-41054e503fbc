import React from "react";
import { Select, SelectItem } from "@heroui/select";

import { ChatModel } from "../../../types/firestore/chatModel";
import { LabelModel } from "../../../types/firestore/labelModel";
import { useAppSelector } from "../../../store/hooks";

interface LabelAssignmentPanelProps {
  chat: ChatModel | null;
}

const LabelAssignmentPanel: React.FC<LabelAssignmentPanelProps> = ({
  chat,
}) => {
  const { labels } = useAppSelector((state) => state.labels);

  const handleLabelChange = (keys: any) => {
    // We expect a single selection. 'keys' behaves like a Set.
    if (keys.size === 0) {
      console.log("No label selected");

      // Optionally, handle the case where the label is cleared if the Select component supports it
      return;
    }
    const selectedLabelId = keys.values().next().value;

    // TODO: Implement backend logic to update chat label
    console.log("Selected label ID:", selectedLabelId);
    console.log("Chat ID:", chat?.id);
  };

  const getSelectedLabelKey = () => {
    if (!chat || !chat.label) {
      return new Set<string>(); // Return an empty set for no selection
    }

    // ChatModel has a single `label` property, not an array of `labels`
    return new Set([chat.label.id]);
  };

  return (
    <div className="p-4 border-b border-divider">
      <h3 className="text-lg font-semibold mb-2">Assign Label</h3>
      <Select
        label="Select a label"
        placeholder="Choose a label"
        selectedKeys={getSelectedLabelKey()}
        variant="bordered"
        onSelectionChange={handleLabelChange}
      >
        {labels.map((label: LabelModel) => (
          <SelectItem key={label.id}>{label.name}</SelectItem>
        ))}
      </Select>
    </div>
  );
};

export default LabelAssignmentPanel;
