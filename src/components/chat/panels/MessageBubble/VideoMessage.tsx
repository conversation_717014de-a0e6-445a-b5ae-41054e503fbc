import type { NormalizedMessage } from "../../../../types";

import React, { useState, useEffect } from "react";
import { RiVideoLine } from "react-icons/ri";

import { getMessageMedia } from "@/services/main/messageMainService";

interface VideoMessageProps {
  message: NormalizedMessage;
}

const VideoMessage: React.FC<VideoMessageProps> = ({ message }) => {
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const caption = message.video?.caption;
  const mediaId = message.video?.id;

  useEffect(() => {
    if (!mediaId) {
      return;
    }

    const fetchVideo = async () => {
      setLoading(true);
      setError(null);

      try {
        // Extract projectId from the URL
        const urlParts = window.location.pathname.split("/");
        const projectIdIndex = urlParts.indexOf("project");
        const projectId =
          projectIdIndex !== -1 && projectIdIndex + 1 < urlParts.length
            ? urlParts[projectIdIndex + 1]
            : null;

        if (!projectId) {
          setError("Project ID not found");
          setLoading(false);

          return;
        }

        const response = await getMessageMedia(
          projectId,
          message.chatId,
          mediaId,
        );

        const blob = response.data;
        const url = URL.createObjectURL(blob);

        setVideoUrl(url);
      } catch {
        // Log error for debugging
        // In production, consider using a proper error tracking service
        setError("Failed to load video");
      } finally {
        setLoading(false);
      }
    };

    fetchVideo();

    // Clean up the URL object when the component unmounts
    return () => {
      if (videoUrl) {
        URL.revokeObjectURL(videoUrl);
      }
    };
  }, [mediaId, message.chatId]);

  if (loading) {
    return (
      <div className="flex flex-col space-y-2">
        <div className="relative bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse">
          <div className="w-full h-48 rounded-lg flex items-center justify-center">
            <div className="text-gray-500 dark:text-gray-300">
              Loading video...
            </div>
          </div>
        </div>
        {caption && (
          <div className="text-sm opacity-75 break-words whitespace-pre-wrap">
            {caption}
          </div>
        )}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col space-y-2">
        <div className="flex items-center space-x-2 p-2 bg-red-100 dark:bg-red-900 rounded-lg">
          <RiVideoLine className="h-5 w-5 text-red-600 dark:text-red-300" />
          <span className="text-sm font-medium text-red-700 dark:text-red-200">
            {error}
          </span>
        </div>
        {caption && (
          <div className="text-sm opacity-75 break-words whitespace-pre-wrap">
            {caption}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col space-y-2">
      {videoUrl ? (
        <div className="relative">
          <video
            controls
            className="max-w-full rounded-lg shadow-sm"
            preload="metadata"
            src={videoUrl}
          >
            <track kind="captions" />
            Your browser does not support the video element.
          </video>
          {!caption && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-10 transition-all rounded-lg">
              <RiVideoLine className="h-8 w-8 text-white opacity-70" />
            </div>
          )}
        </div>
      ) : (
        <div className="flex items-center space-x-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
          <RiVideoLine className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
            Video
          </span>
        </div>
      )}

      {caption && (
        <div className="text-sm opacity-75 break-words whitespace-pre-wrap">
          {caption}
        </div>
      )}
    </div>
  );
};

VideoMessage.displayName = "VideoMessage";

export default VideoMessage;
