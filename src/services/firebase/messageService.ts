import type {
  MessageContentType,
  MessageStatus,
  NormalizedMessage,
} from "@/types/chat";

import {
  collection,
  onSnapshot,
  orderBy,
  query,
  Unsubscribe,
} from "firebase/firestore";

import { MessageModelFirestore } from "@/types/firestore/messageModel.ts";
import { firestore } from "@/services/firebase/firebase.ts";

const subscribeToMessages = (
  projectId: string,
  chatId: string,
  callback: (messages: NormalizedMessage[]) => void,
  onError?: (error: Error) => void,
): Unsubscribe => {
  const messagesQuery = query(
    collection(
      firestore,
      "projects",
      projectId,
      "chats",
      chatId,
      "messages",
    ).withConverter(MessageModelFirestore.converter()),
    orderBy("message.timestamp", "asc"),
  );

  return onSnapshot(
    messagesQuery,
    (snapshot) => {
      const messages = snapshot.docs.map((doc) => {
        const data = doc.data();

        // Convert TMessageType to MessageContentType
        let type: MessageContentType;

        if (data.message.type === "text") type = "text";
        else if (data.message.type === "image") type = "image";
        else if (data.message.type === "audio") type = "audio";
        else if (data.message.type === "video") type = "video";
        else if (data.message.type === "document") type = "document";
        else if (data.message.type === "sticker") type = "sticker";
        else if (data.message.type === "location") type = "location";
        else if (data.message.type === "contacts") type = "contact";
        else if (data.message.type === "button") type = "button_reply";
        else if (data.message.type === "interactive") type = "button_reply";
        else if (data.message.type === "reaction") type = "reaction";
        else if (data.message.type === "system") type = "system";
        else if (data.message.type === "unsupported") type = "unsupported";
        else type = "text"; // fallback

        // Determine sender type based on direction
        const senderType: "user" | "contact" =
          data.direction === "out" ? "user" : "contact";

        // Extract sender information from sentBy for outgoing messages or from message.from for incoming
        let senderId, senderName, senderPhoneNumber, senderWhatsappId;

        if (data.direction === "out" && data.sentBy) {
          senderId = data.sentBy.uid;
          senderName = data.sentBy.name;
          senderPhoneNumber = "";
          senderWhatsappId = "";
        } else {
          senderId = data.message.from;
          senderName = data.message.from;
          senderPhoneNumber = data.message.from;
          senderWhatsappId = data.message.from;
        }

        // Get message status from statuses
        let status: MessageStatus = "sent";

        if (data.statuses) {
          status = data.statuses.latest as MessageStatus;
        }

        // Create normalized message with both legacy and new structure
        const normalizedMessage: NormalizedMessage = {
          id: doc.id,
          chatId,
          whatsappMessageId: data.message.id,

          // New type-specific content structure
          ...(data.message.text && { text: { body: data.message.text.body } }),
          ...(data.message.image && {
            image: {
              id: data.message.image.id,
              mimeType: data.message.image.mime_type,
              ...(data.message.image.sha256 && {
                sha256: data.message.image.sha256,
              }),
              caption: data.message.image.caption,
            },
          }),
          ...(data.message.video && {
            video: {
              id: data.message.video.id,
              mimeType: data.message.video.mime_type,
              ...(data.message.video.sha256 && {
                sha256: data.message.video.sha256,
              }),
              caption: data.message.video.caption,
            },
          }),
          ...(data.message.audio && {
            audio: {
              id: data.message.audio.id,
              mimeType: data.message.audio.mime_type,
              ...(data.message.audio.sha256 && {
                sha256: data.message.audio.sha256,
              }),
            },
          }),
          ...(data.message.document && {
            document: {
              id: data.message.document.id,
              mimeType: data.message.document.mime_type,
              ...(data.message.document.sha256 && {
                sha256: data.message.document.sha256,
              }),
              caption: data.message.document.caption,
              filename: data.message.document.filename,
            },
          }),
          ...(data.message.sticker && {
            sticker: {
              id: data.message.sticker.id,
              mimeType: data.message.sticker.mime_type,
              ...(data.message.sticker.sha256 && {
                sha256: data.message.sticker.sha256,
              }),
            },
          }),
          ...(data.message.location && {
            location: {
              latitude: data.message.location.latitude,
              longitude: data.message.location.longitude,
              name: data.message.location.name,
              address: data.message.location.address,
            },
          }),
          ...(data.message.contacts && {
            contact: data.message.contacts.map((contact) => ({
              name: {
                formattedName: contact.name.formatted_name,
                firstName: contact.name.first_name,
                lastName: contact.name.last_name,
                middleName: contact.name.middle_name,
                suffix: contact.name.suffix,
                prefix: contact.name.prefix,
              },
              phones: contact.phones.map((phone) => ({
                phone: phone.phone,
                type: phone.type,
                waId: phone.wa_id,
              })),
              emails: contact.emails?.map((email) => ({
                email: email.email,
                type: email.type,
              })),
              urls: contact.urls?.map((url) => ({
                url: url.url,
                type: url.type,
              })),
            }))[0],
          }),
          ...(data.message.reaction && {
            reaction: {
              messageId: data.message.reaction.message_id,
              emoji: data.message.reaction.emoji,
            },
          }),
          ...(data.message.interactive && {
            interactive: {
              type: data.message.interactive.type,
              ...(data.message.interactive.list_reply && {
                listReply: {
                  id: data.message.interactive.list_reply.id,
                  title: data.message.interactive.list_reply.title,
                  description: data.message.interactive.list_reply.description,
                },
              }),
              ...(data.message.interactive.button_reply && {
                buttonReply: {
                  payload: data.message.interactive.button_reply.id,
                  text: data.message.interactive.button_reply.title,
                },
              }),
            },
          }),
          ...(data.message.button && {
            interactive: {
              type: "button_reply",
              buttonReply: {
                payload: data.message.button.payload,
                text: data.message.button.text,
              },
            },
          }),

          type,
          sender: {
            id: senderId,
            name: senderName,
            phoneNumber: senderPhoneNumber,
            whatsappId: senderWhatsappId,
            type: senderType,
          },
          timestamp: data.message.timestamp,
          status,

          // New status details structure
          ...(data.statuses && {
            statusDetails: {
              latest: data.statuses.latest,
              timestamp: data.statuses.timestamp,
              details: data.statuses.details
                ? {
                    sent: data.statuses.details.sent,
                    delivered: data.statuses.details.delivered,
                    read: data.statuses.details.read,
                    failed: data.statuses.details.failed,
                  }
                : null,
            },
          }),

          // Direction tracking
          direction: data.direction,

          // Error field for unsupported messages
          errors: data.message.errors || [],

          // Metadata with referral data
          metadata: {
            ...(data.message.context && {
              forwarded: data.message.context.forwarded,
              frequentlyForwarded: data.message.context.frequently_forwarded,
              isFromAd: !!data.message.referral,
            }),
            ...(data.message.referral && {
              referralData: {
                sourceType: data.message.referral.source_type,
                sourceId: data.message.referral.source_id,
                sourceUrl: data.message.referral.source_url,
                body: data.message.referral.body,
                headline: data.message.referral.headline,
                mediaType: data.message.referral.media_type,
                imageUrl: data.message.referral.image_url,
                videoUrl: data.message.referral.video_url,
                thumbnailUrl: data.message.referral.thumbnail_url,
                ctwaClid: data.message.referral.ctwa_clid,
              },
            }),
          },
        };

        return normalizedMessage;
      });

      callback(messages);
    },
    (error) => {
      // Log error for debugging
      // In production, consider using a proper error tracking service
      onError?.(error);
    },
  );
};

export default subscribeToMessages;
