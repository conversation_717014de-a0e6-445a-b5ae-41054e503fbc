# Chat API Endpoint Documentation

## Overview

Chat API endpoints provide functionality to manage chat conversations within a project. These endpoints allow users to retrieve all chats and update chat labels. The API is built with NestJS and uses Firebase Firestore as the database.

## Base URL

```
/user/project/:projectId
```

## Authentication & Authorization

All endpoints require:
- **Authentication**: Valid user token (via `AuthGuard`)
- **Project Authorization**: User must have access to the specified project (via `ProjectGuard`)
- **Chat Authorization**: For chat-specific operations, the chat must exist and be accessible (via `ChatGuard`)

## Endpoints

### 1. Get All Chats

Retrieve all chat conversations for a specific project.

**Endpoint:** `GET /user/project/:projectId/chat`

**Authentication:** Required

**Parameters:**
- Path Parameters:
  - `projectId` (string, required): The unique identifier of the project

**Response:**
```json
{
  "message": "Successfully get all chats",
  "data": [
    {
      "id": "string",
      "phoneNumber": "string",
      "name": "string",
      "createdAt": "2025-01-01T00:00:00.000Z",
      "updatedAt": "2025-01-01T00:00:00.000Z",
      "lastMessage": {
        "id": "string",
        "content": "string",
        "timestamp": "2025-01-01T00:00:00.000Z",
        "direction": "incoming" | "outgoing",
        "status": "sent" | "delivered" | "read" | "failed",
        "mediaUrl": "string | null",
        "mediaType": "string | null",
        "metadata": "object | null"
      } | null,
      "label": {
        "id": "string",
        "name": "string"
      } | null
    }
  ]
}
```

**Success Response:**
- **Status Code:** `200 OK`
- **Body:** Array of chat objects

**Error Responses:**
- **Status Code:** `401 Unauthorized` - Invalid or missing authentication token
- **Status Code:** `403 Forbidden` - User doesn't have access to the project
- **Status Code:** `404 Not Found` - Project not found

**Test Cases Coverage:**
- ✅ Returns at least one chat
- ✅ Validates response structure (message and data properties)
- ✅ Ensures data is an array
- ✅ Validates basic chat object properties

---

### 2. Update Chat Label

Update the label associated with a specific chat conversation.

**Endpoint:** `PUT /user/project/:projectId/chat/:chatId/label`

**Authentication:** Required

**Parameters:**
- Path Parameters:
  - `projectId` (string, required): The unique identifier of the project
  - `chatId` (string, required): The unique identifier of the chat (typically phoneNumber)

**Request Body:**
```json
{
  "labelId": "string"
}
```

**DTO Validation:**
- `labelId` (string, required): Must be a non-empty string
- Uses Zod schema validation with error message: "Label ID is required"

**Response:**
```json
{
  "message": "Successfully updated chat label",
  "data": {
    "id": "string",
    "phoneNumber": "string",
    "name": "string",
    "createdAt": "2025-01-01T00:00:00.000Z",
    "updatedAt": "2025-01-01T00:00:00.000Z",
    "lastMessage": {
      "id": "string",
      "content": "string",
      "timestamp": "2025-01-01T00:00:00.000Z",
      "direction": "incoming" | "outgoing",
      "status": "sent" | "delivered" | "read" | "failed",
      "mediaUrl": "string | null",
      "mediaType": "string | null",
      "metadata": "object | null"
    } | null,
    "label": {
      "id": "string",
      "name": "string"
    }
  }
}
```

**Success Response:**
- **Status Code:** `200 OK`
- **Body:** Updated chat object with new label and updated timestamp

**Error Responses:**
- **Status Code:** `400 Bad Request` 
  - Invalid request body format
  - Label ID does not exist in project labels
  - Empty or missing labelId
- **Status Code:** `401 Unauthorized` - Invalid or missing authentication token
- **Status Code:** `403 Forbidden` - User doesn't have access to the project
- **Status Code:** `404 Not Found` 
  - Project not found
  - Chat not found

**Business Logic:**
1. Validates that the specified label exists in the project's labels collection
2. Validates that the chat exists in the project's chats collection
3. Updates the chat with the new label information
4. Automatically updates the `updatedAt` timestamp
5. Preserves all other chat data (phoneNumber, name, createdAt, lastMessage, etc.)

**Test Cases Coverage:**
- ✅ Successfully updates chat label with valid labelId
- ✅ Validates response structure and message
- ✅ Verifies label object is properly updated with correct id and name
- ✅ Ensures updatedAt timestamp is updated
- ✅ Throws BadRequestException for non-existent labelId
- ✅ Throws NotFoundException for non-existent chatId
- ✅ Validates request body with required labelId
- ✅ Handles empty labelId validation
- ✅ Handles missing labelId validation
- ✅ Preserves other chat data when updating label
- ✅ Handles multiple label updates to the same chat
- ✅ Verifies label changes are properly applied

---

## Data Models

### ChatModel

```typescript
interface ChatModel {
  id: string;
  phoneNumber: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  lastMessage: MessageModel | null;
  label: ChatLabel | null;
}
```

### ChatLabel

```typescript
interface ChatLabel {
  id: string;
  name: string;
}
```

### UpdateChatLabelDto

```typescript
interface UpdateChatLabelDto {
  labelId: string; // Required, minimum 1 character
}
```

## Security Considerations

1. **Authentication**: All endpoints require valid JWT tokens
2. **Authorization**: Project-level access control ensures users can only access their own projects
3. **Chat Guard**: Ensures chat-specific operations are only performed on existing chats
4. **Input Validation**: Zod schema validation prevents malformed requests
5. **Label Validation**: Ensures labels exist before assignment to chats

## Error Handling

The API uses standard HTTP status codes and provides consistent error responses:

- **400 Bad Request**: Invalid input data or validation errors
- **401 Unauthorized**: Authentication failure
- **403 Forbidden**: Authorization failure
- **404 Not Found**: Resource not found (project, chat, or label)
- **500 Internal Server Error**: Server-side errors

## Testing

The endpoints are thoroughly tested with the following test scenarios:

### Get Chats Tests:
- Response structure validation
- Data type verification
- Minimum data presence verification

### Update Chat Label Tests:
- Successful update scenarios
- Error handling for invalid inputs
- Data preservation during updates
- Multiple update scenarios
- Edge case handling
- Validation of all business rules

All tests use Firebase Firestore test data and clean up after execution to maintain test isolation.

## Usage Examples

### Get All Chats

```javascript
// Using fetch
const response = await fetch('/user/project/123/chat', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
console.log(result.data); // Array of chats
```

### Update Chat Label

```javascript
// Using fetch
const response = await fetch('/user/project/123/chat/6281234567890/label', {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    labelId: 'label-123'
  })
});

const result = await response.json();
console.log(result.data); // Updated chat with new label
```

## Rate Limiting

Currently, no rate limiting is implemented for these endpoints. Consider implementing rate limiting for production use to prevent abuse.

## Version History

- **v1.0.0**: Initial implementation with basic chat management functionality
  - GET /user/project/:projectId/chat
  - PUT /user/project/:projectId/chat/:chatId/label

## Future Enhancements

1. **Pagination**: Add pagination support for getChats endpoint
2. **Filtering**: Add filtering options for chats (by label, date range, etc.)
3. **Sorting**: Add sorting options for chat lists
4. **Batch Operations**: Add support for updating multiple chat labels at once
5. **Chat Search**: Add search functionality for chat content
6. **Webhook Integration**: Real-time updates for chat changes
